import { createBrowserRouter } from "react-router-dom"
import App from "./App"
import AuthGuard from "./guards/AuthGuard"
import GuestGuard from "./guards/GuestGuard"
import Home from "./pages/Home"
import Login from "./pages/Login"
import Register from "./pages/Register"

export const router = createBrowserRouter([
  {
    path: "/",
    Component: App,
    children: [
      {
        path: "/",
        element: <GuestGuard><Login /></GuestGuard>,
        index: true,
      },
      {
        path: "/register",
        element: <GuestGuard><Register /></GuestGuard>,
      },
      {
        path: "/home",
        element: <AuthGuard><Home /></AuthGuard>,
      },
    ]
  },
])
